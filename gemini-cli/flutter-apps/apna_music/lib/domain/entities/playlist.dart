import 'package:equatable/equatable.dart';
import 'song.dart';

/// Playlist entity representing a collection of songs
class Playlist extends Equatable {
  final String id;
  final String name;
  final String? description;
  final List<String> songIds;
  final String? thumbnailUrl;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isSystemPlaylist;
  final PlaylistType type;

  const Playlist({
    required this.id,
    required this.name,
    this.description,
    required this.songIds,
    this.thumbnailUrl,
    required this.createdAt,
    required this.updatedAt,
    this.isSystemPlaylist = false,
    this.type = PlaylistType.custom,
  });

  /// Create a copy of the playlist with updated properties
  Playlist copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? songIds,
    String? thumbnailUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSystemPlaylist,
    PlaylistType? type,
  }) {
    return Playlist(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      songIds: songIds ?? this.songIds,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSystemPlaylist: isSystemPlaylist ?? this.isSystemPlaylist,
      type: type ?? this.type,
    );
  }

  /// Add a song to the playlist
  Playlist addSong(String songId) {
    if (songIds.contains(songId)) return this;
    
    return copyWith(
      songIds: [...songIds, songId],
      updatedAt: DateTime.now(),
    );
  }

  /// Remove a song from the playlist
  Playlist removeSong(String songId) {
    if (!songIds.contains(songId)) return this;
    
    return copyWith(
      songIds: songIds.where((id) => id != songId).toList(),
      updatedAt: DateTime.now(),
    );
  }

  /// Move a song to a different position in the playlist
  Playlist moveSong(int oldIndex, int newIndex) {
    if (oldIndex < 0 || oldIndex >= songIds.length ||
        newIndex < 0 || newIndex >= songIds.length) {
      return this;
    }

    final newSongIds = List<String>.from(songIds);
    final song = newSongIds.removeAt(oldIndex);
    newSongIds.insert(newIndex, song);

    return copyWith(
      songIds: newSongIds,
      updatedAt: DateTime.now(),
    );
  }

  /// Check if the playlist contains a specific song
  bool containsSong(String songId) => songIds.contains(songId);

  /// Get the number of songs in the playlist
  int get songCount => songIds.length;

  /// Check if the playlist is empty
  bool get isEmpty => songIds.isEmpty;

  /// Check if the playlist is not empty
  bool get isNotEmpty => songIds.isNotEmpty;

  /// Get total duration of all songs (requires song list)
  Duration getTotalDuration(List<Song> songs) {
    int totalSeconds = 0;
    for (final songId in songIds) {
      final song = songs.firstWhere(
        (s) => s.id == songId,
        orElse: () => const Song(
          id: '',
          title: '',
          artist: '',
          duration: Duration.zero,
        ),
      );
      totalSeconds += song.duration.inSeconds;
    }
    return Duration(seconds: totalSeconds);
  }

  /// Get formatted total duration string
  String getFormattedTotalDuration(List<Song> songs) {
    final duration = getTotalDuration(songs);
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        songIds,
        thumbnailUrl,
        createdAt,
        updatedAt,
        isSystemPlaylist,
        type,
      ];

  @override
  String toString() {
    return 'Playlist(id: $id, name: $name, songCount: $songCount)';
  }
}

/// Enum for different types of playlists
enum PlaylistType {
  custom,
  favorites,
  recentlyPlayed,
  mostPlayed,
  downloaded,
  queue,
}

/// Extension to get display names for playlist types
extension PlaylistTypeExtension on PlaylistType {
  String get displayName {
    switch (this) {
      case PlaylistType.custom:
        return 'Custom';
      case PlaylistType.favorites:
        return 'Favorites';
      case PlaylistType.recentlyPlayed:
        return 'Recently Played';
      case PlaylistType.mostPlayed:
        return 'Most Played';
      case PlaylistType.downloaded:
        return 'Downloaded';
      case PlaylistType.queue:
        return 'Queue';
    }
  }

  String get hackerDisplayName {
    switch (this) {
      case PlaylistType.custom:
        return 'CUSTOM_PLAYLIST.exe';
      case PlaylistType.favorites:
        return 'FAVORITES_CACHE.dat';
      case PlaylistType.recentlyPlayed:
        return 'RECENT_ACCESS.log';
      case PlaylistType.mostPlayed:
        return 'TOP_TRACKS.db';
      case PlaylistType.downloaded:
        return 'LOCAL_STORAGE.bin';
      case PlaylistType.queue:
        return 'PLAYBACK_QUEUE.tmp';
    }
  }
}
