import 'package:equatable/equatable.dart';

/// Song entity representing a music track
class Song extends Equatable {
  final String id;
  final String title;
  final String artist;
  final String? album;
  final Duration duration;
  final String? thumbnailUrl;
  final String? audioUrl;
  final String? localPath;
  final bool isDownloaded;
  final bool isFavorite;
  final DateTime? addedAt;
  final DateTime? lastPlayedAt;
  final int playCount;
  final String? youtubeId;
  final String? description;
  final int? viewCount;
  final String? channelName;
  final String? channelId;

  const Song({
    required this.id,
    required this.title,
    required this.artist,
    this.album,
    required this.duration,
    this.thumbnailUrl,
    this.audioUrl,
    this.localPath,
    this.isDownloaded = false,
    this.isFavorite = false,
    this.addedAt,
    this.lastPlayedAt,
    this.playCount = 0,
    this.youtubeId,
    this.description,
    this.viewCount,
    this.channelName,
    this.channelId,
  });

  /// Create a copy of the song with updated properties
  Song copyWith({
    String? id,
    String? title,
    String? artist,
    String? album,
    Duration? duration,
    String? thumbnailUrl,
    String? audioUrl,
    String? localPath,
    bool? isDownloaded,
    bool? isFavorite,
    DateTime? addedAt,
    DateTime? lastPlayedAt,
    int? playCount,
    String? youtubeId,
    String? description,
    int? viewCount,
    String? channelName,
    String? channelId,
  }) {
    return Song(
      id: id ?? this.id,
      title: title ?? this.title,
      artist: artist ?? this.artist,
      album: album ?? this.album,
      duration: duration ?? this.duration,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      audioUrl: audioUrl ?? this.audioUrl,
      localPath: localPath ?? this.localPath,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      isFavorite: isFavorite ?? this.isFavorite,
      addedAt: addedAt ?? this.addedAt,
      lastPlayedAt: lastPlayedAt ?? this.lastPlayedAt,
      playCount: playCount ?? this.playCount,
      youtubeId: youtubeId ?? this.youtubeId,
      description: description ?? this.description,
      viewCount: viewCount ?? this.viewCount,
      channelName: channelName ?? this.channelName,
      channelId: channelId ?? this.channelId,
    );
  }

  /// Check if the song has a valid audio source
  bool get hasAudioSource => audioUrl != null || localPath != null;

  /// Get the primary audio source (local path takes precedence)
  String? get primaryAudioSource => localPath ?? audioUrl;

  /// Check if the song is available for playback
  bool get isPlayable => hasAudioSource;

  /// Get formatted duration string
  String get formattedDuration {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Get display artist (fallback to channel name if artist is empty)
  String get displayArtist {
    if (artist.isNotEmpty) return artist;
    if (channelName != null && channelName!.isNotEmpty) return channelName!;
    return 'Unknown Artist';
  }

  /// Get display title (truncated if too long)
  String getDisplayTitle({int maxLength = 50}) {
    if (title.length <= maxLength) return title;
    return '${title.substring(0, maxLength - 3)}...';
  }

  @override
  List<Object?> get props => [
        id,
        title,
        artist,
        album,
        duration,
        thumbnailUrl,
        audioUrl,
        localPath,
        isDownloaded,
        isFavorite,
        addedAt,
        lastPlayedAt,
        playCount,
        youtubeId,
        description,
        viewCount,
        channelName,
        channelId,
      ];

  @override
  String toString() {
    return 'Song(id: $id, title: $title, artist: $artist, duration: $duration)';
  }
}
