/// Core application constants for the hacker-themed music player
class AppConstants {
  // App Information
  static const String appName = 'Apna Music';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Hacker-themed music player';

  // Database
  static const String hiveDatabaseName = 'apna_music_db';
  static const String songsBoxName = 'songs';
  static const String playlistsBoxName = 'playlists';
  static const String favoritesBoxName = 'favorites';
  static const String settingsBoxName = 'settings';

  // Storage
  static const String downloadsFolderName = 'ApnaMusic';
  static const String thumbnailsFolderName = 'Thumbnails';
  
  // Audio
  static const Duration seekDuration = Duration(seconds: 10);
  static const double defaultVolume = 0.8;
  static const int maxRecentSongs = 50;
  
  // YouTube
  static const int maxSearchResults = 20;
  static const Duration searchDebounceTime = Duration(milliseconds: 500);
  
  // UI
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration glitchAnimationDuration = Duration(milliseconds: 150);
  static const double borderRadius = 8.0;
  static const double cardElevation = 4.0;
  
  // Hacker Theme
  static const String hackerFontFamily = 'Courier';
  static const double terminalFontSize = 14.0;
  static const int matrixRainSpeed = 100; // milliseconds
  static const int systemHudUpdateInterval = 1000; // milliseconds
}

/// API and Network constants
class NetworkConstants {
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 2);
}

/// Error messages
class ErrorMessages {
  static const String noInternetConnection = 'No internet connection';
  static const String somethingWentWrong = 'Something went wrong';
  static const String videoNotAvailable = 'Video not available';
  static const String downloadFailed = 'Download failed';
  static const String playbackError = 'Playback error occurred';
  static const String permissionDenied = 'Permission denied';
  static const String storageNotAvailable = 'Storage not available';
}

/// Success messages
class SuccessMessages {
  static const String downloadCompleted = 'Download completed successfully';
  static const String playlistCreated = 'Playlist created';
  static const String songAddedToPlaylist = 'Song added to playlist';
  static const String songRemovedFromPlaylist = 'Song removed from playlist';
  static const String addedToFavorites = 'Added to favorites';
  static const String removedFromFavorites = 'Removed from favorites';
}
