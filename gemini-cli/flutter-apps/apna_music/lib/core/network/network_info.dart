import 'dart:io';

/// Abstract class for network connectivity information
abstract class NetworkInfo {
  Future<bool> get isConnected;
}

/// Implementation of NetworkInfo using InternetAddress lookup
class NetworkInfoImpl implements NetworkInfo {
  @override
  Future<bool> get isConnected async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }
}

/// Network utility functions
class NetworkUtils {
  /// Check if the device has internet connectivity
  static Future<bool> hasInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }

  /// Check if a specific host is reachable
  static Future<bool> isHostReachable(String host) async {
    try {
      final result = await InternetAddress.lookup(host);
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }

  /// Get network connection type (simplified)
  static Future<NetworkConnectionType> getConnectionType() async {
    // This is a simplified implementation
    // In a real app, you might want to use connectivity_plus package
    final hasConnection = await hasInternetConnection();
    return hasConnection 
        ? NetworkConnectionType.wifi 
        : NetworkConnectionType.none;
  }
}

/// Enum for network connection types
enum NetworkConnectionType {
  none,
  wifi,
  mobile,
  ethernet,
}
