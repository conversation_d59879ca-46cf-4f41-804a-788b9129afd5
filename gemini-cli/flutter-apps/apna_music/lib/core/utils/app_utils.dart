import 'dart:math';
import 'package:flutter/material.dart';

/// Utility functions for the application
class AppUtils {
  /// Format duration to MM:SS or HH:MM:SS format
  static String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');

    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  /// Format file size to human readable format
  static String formatFileSize(int bytes) {
    if (bytes <= 0) return '0 B';

    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    final i = (log(bytes) / log(1024)).floor();

    return '${(bytes / pow(1024, i)).toStringAsFixed(1)} ${suffixes[i]}';
  }

  /// Generate a random hacker-style ID
  static String generateHackerId() {
    const chars = '0123456789ABCDEF';
    final random = Random();
    return List.generate(
      8,
      (index) => chars[random.nextInt(chars.length)],
    ).join();
  }

  /// Get a random hacker-style message
  static String getRandomHackerMessage() {
    final messages = [
      'ACCESSING MAINFRAME...',
      'BYPASSING FIREWALL...',
      'DECRYPTING DATA...',
      'ESTABLISHING SECURE CONNECTION...',
      'INITIALIZING NEURAL NETWORK...',
      'SCANNING FOR VULNERABILITIES...',
      'UPLOADING PAYLOAD...',
      'EXECUTING BACKDOOR...',
      'INFILTRATING SYSTEM...',
      'CRACKING ENCRYPTION...',
    ];

    final random = Random();
    return messages[random.nextInt(messages.length)];
  }

  /// Generate random system stats for HUD
  static Map<String, dynamic> generateSystemStats() {
    final random = Random();
    return {
      'cpu': random.nextInt(100),
      'ram': random.nextInt(100),
      'network': random.nextInt(100),
      'disk': random.nextInt(100),
    };
  }

  /// Validate YouTube URL
  static bool isValidYouTubeUrl(String url) {
    final youtubeRegex = RegExp(
      r'^(https?://)?(www\.)?(youtube\.com/watch\?v=|youtu\.be/|youtube\.com/embed/|youtube\.com/v/)([a-zA-Z0-9_-]{11})',
      caseSensitive: false,
    );
    return youtubeRegex.hasMatch(url);
  }

  /// Extract YouTube video ID from URL
  static String? extractYouTubeVideoId(String url) {
    final regexes = [
      RegExp(r'youtube\.com/watch\?v=([a-zA-Z0-9_-]{11})'),
      RegExp(r'youtu\.be/([a-zA-Z0-9_-]{11})'),
      RegExp(r'youtube\.com/embed/([a-zA-Z0-9_-]{11})'),
      RegExp(r'youtube\.com/v/([a-zA-Z0-9_-]{11})'),
    ];

    for (final regex in regexes) {
      final match = regex.firstMatch(url);
      if (match != null) {
        return match.group(1);
      }
    }
    return null;
  }

  /// Show hacker-styled snackbar
  static void showHackerSnackBar(
    BuildContext context,
    String message, {
    Color? backgroundColor,
    Color? textColor,
    Duration duration = const Duration(seconds: 3),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(
            color: textColor ?? Colors.green,
            fontFamily: 'Courier',
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: backgroundColor ?? Colors.black87,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(color: textColor ?? Colors.green, width: 1),
        ),
      ),
    );
  }

  /// Sanitize filename for storage
  static String sanitizeFilename(String filename) {
    // Remove invalid characters for file names
    return filename
        .replaceAll(RegExp(r'[<>:"/\\|?*]'), '')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
  }

  /// Generate thumbnail URL from YouTube video ID
  static String getYouTubeThumbnailUrl(
    String videoId, {
    String quality = 'hqdefault',
  }) {
    return 'https://img.youtube.com/vi/$videoId/$quality.jpg';
  }

  /// Debounce function for search
  static void debounce(Duration duration, VoidCallback callback) {
    // This is a simplified debounce implementation
    // In a real app, you might want to use a more sophisticated approach
    Future.delayed(duration, callback);
  }
}
