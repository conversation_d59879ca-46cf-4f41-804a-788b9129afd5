import 'package:flutter/material.dart';

/// Hacker/Cyberpunk theme colors for the music player
class AppColors {
  // Primary hacker theme colors
  static const Color hackerGreen = Color(0xFF00FF00);
  static const Color matrixGreen = Color(0xFF00FF41);
  static const Color neonCyan = Color(0xFF00FFFF);
  static const Color cyberpunkPurple = Color(0xFF9D00FF);
  static const Color dangerRed = Color(0xFFFF0040);
  static const Color warningOrange = Color(0xFFFF8C00);

  // Background colors
  static const Color primaryBackground = Color(0xFF0A0A0A);
  static const Color secondaryBackground = Color(0xFF1A1A1A);
  static const Color cardBackground = Color(0xFF2A2A2A);
  static const Color surfaceBackground = Color(0xFF1E1E1E);

  // Text colors
  static const Color primaryText = Color(0xFFFFFFFF);
  static const Color secondaryText = Color(0xFFB0B0B0);
  static const Color accentText = hackerGreen;
  static const Color terminalText = matrixGreen;

  // UI Element colors
  static const Color borderColor = Color(0xFF404040);
  static const Color dividerColor = Color(0xFF303030);
  static const Color shadowColor = Color(0x40000000);
  static const Color overlayColor = Color(0x80000000);

  // Interactive colors
  static const Color buttonPrimary = hackerGreen;
  static const Color buttonSecondary = neonCyan;
  static const Color buttonDanger = dangerRed;
  static const Color rippleColor = Color(0x40FFFFFF);

  // Status colors
  static const Color success = hackerGreen;
  static const Color error = dangerRed;
  static const Color warning = warningOrange;
  static const Color info = neonCyan;

  // Player specific colors
  static const Color progressBarActive = hackerGreen;
  static const Color progressBarInactive = Color(0xFF404040);
  static const Color volumeSlider = neonCyan;
  static const Color playButtonGlow = hackerGreen;

  // Gradient colors for hacker effects
  static const LinearGradient hackerGradient = LinearGradient(
    colors: [hackerGreen, neonCyan],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient cyberpunkGradient = LinearGradient(
    colors: [cyberpunkPurple, dangerRed],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient matrixGradient = LinearGradient(
    colors: [matrixGreen, hackerGreen],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  // Glow effects
  static BoxShadow get hackerGlow => BoxShadow(
        color: hackerGreen.withOpacity(0.5),
        blurRadius: 10,
        spreadRadius: 2,
      );

  static BoxShadow get cyanGlow => BoxShadow(
        color: neonCyan.withOpacity(0.5),
        blurRadius: 10,
        spreadRadius: 2,
      );

  static BoxShadow get purpleGlow => BoxShadow(
        color: cyberpunkPurple.withOpacity(0.5),
        blurRadius: 10,
        spreadRadius: 2,
      );
}

/// Color schemes for different hacker theme modes
class HackerColorSchemes {
  static const ColorScheme matrix = ColorScheme.dark(
    primary: AppColors.matrixGreen,
    secondary: AppColors.hackerGreen,
    surface: AppColors.primaryBackground,
    background: AppColors.primaryBackground,
    error: AppColors.dangerRed,
    onPrimary: AppColors.primaryBackground,
    onSecondary: AppColors.primaryBackground,
    onSurface: AppColors.primaryText,
    onBackground: AppColors.primaryText,
    onError: AppColors.primaryText,
  );

  static const ColorScheme cyberpunk = ColorScheme.dark(
    primary: AppColors.cyberpunkPurple,
    secondary: AppColors.neonCyan,
    surface: AppColors.primaryBackground,
    background: AppColors.primaryBackground,
    error: AppColors.dangerRed,
    onPrimary: AppColors.primaryText,
    onSecondary: AppColors.primaryText,
    onSurface: AppColors.primaryText,
    onBackground: AppColors.primaryText,
    onError: AppColors.primaryText,
  );

  static const ColorScheme terminal = ColorScheme.dark(
    primary: AppColors.hackerGreen,
    secondary: AppColors.neonCyan,
    surface: AppColors.primaryBackground,
    background: AppColors.primaryBackground,
    error: AppColors.dangerRed,
    onPrimary: AppColors.primaryBackground,
    onSecondary: AppColors.primaryBackground,
    onSurface: AppColors.terminalText,
    onBackground: AppColors.terminalText,
    onError: AppColors.primaryText,
  );
}
