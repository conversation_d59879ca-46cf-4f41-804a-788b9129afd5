import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';

import '../../core/themes/app_colors.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import 'home_screen.dart';

/// Hacker-themed splash screen with terminal bootloader effect
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _typewriterController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _typewriterAnimation;

  final List<String> _bootMessages = [
    'INITIALIZING NEURAL NETWORK...',
    'LOADING AUDIO CODECS...',
    'ESTABLISHING SECURE CONNECTION...',
    'BYPASSING FIREWALL...',
    'ACCESSING MUSIC DATABASE...',
    'DECRYPTING AUDIO STREAMS...',
    'SYSTEM READY.',
  ];

  int _currentMessageIndex = 0;
  String _currentDisplayText = '';
  Timer? _typewriterTimer;
  Timer? _messageTimer;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startBootSequence();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _typewriterController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _typewriterAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _typewriterController, curve: Curves.easeInOut),
    );

    _fadeController.forward();
  }

  void _startBootSequence() {
    _typeNextMessage();
  }

  void _typeNextMessage() {
    if (_currentMessageIndex >= _bootMessages.length) {
      _navigateToHome();
      return;
    }

    final message = _bootMessages[_currentMessageIndex];
    _currentDisplayText = '';
    int charIndex = 0;

    _typewriterTimer = Timer.periodic(const Duration(milliseconds: 50), (
      timer,
    ) {
      if (charIndex < message.length) {
        setState(() {
          _currentDisplayText = message.substring(0, charIndex + 1);
        });
        charIndex++;
      } else {
        timer.cancel();
        _messageTimer = Timer(const Duration(milliseconds: 800), () {
          _currentMessageIndex++;
          _typeNextMessage();
        });
      }
    });
  }

  void _navigateToHome() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const HomeScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
        transitionDuration: const Duration(milliseconds: 800),
      ),
    );
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _typewriterController.dispose();
    _typewriterTimer?.cancel();
    _messageTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryBackground,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppColors.primaryBackground, Color(0xFF001100)],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const SizedBox(height: 40),
                  _buildLogo(),
                  const SizedBox(height: 30),
                  _buildAppTitle(),
                  const SizedBox(height: 40),
                  _buildTerminalWindow(),
                  const SizedBox(height: 30),
                  _buildSystemInfo(),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(color: AppColors.hackerGreen, width: 2),
          boxShadow: [AppColors.hackerGlow],
        ),
        child: const Icon(
          Icons.music_note,
          size: 60,
          color: AppColors.hackerGreen,
        ),
      ),
    );
  }

  Widget _buildAppTitle() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Column(
        children: [
          Text(
            AppConstants.appName.toUpperCase(),
            style: const TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: AppColors.hackerGreen,
              fontFamily: 'Courier',
              letterSpacing: 4,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'HACKER EDITION v${AppConstants.appVersion}',
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.neonCyan,
              fontFamily: 'Courier',
              letterSpacing: 2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTerminalWindow() {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        color: Colors.black87,
        border: Border.all(color: AppColors.hackerGreen, width: 1),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: AppColors.hackerGreen.withOpacity(0.3),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: const BoxDecoration(
                    color: AppColors.dangerRed,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  width: 12,
                  height: 12,
                  decoration: const BoxDecoration(
                    color: AppColors.warningOrange,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  width: 12,
                  height: 12,
                  decoration: const BoxDecoration(
                    color: AppColors.hackerGreen,
                    shape: BoxShape.circle,
                  ),
                ),
                const Spacer(),
                const Text(
                  'TERMINAL',
                  style: TextStyle(
                    color: AppColors.secondaryText,
                    fontSize: 10,
                    fontFamily: 'Courier',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (int i = 0; i < _currentMessageIndex; i++)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 4.0),
                        child: Text(
                          '> ${_bootMessages[i]}',
                          style: const TextStyle(
                            color: AppColors.hackerGreen,
                            fontSize: 12,
                            fontFamily: 'Courier',
                          ),
                        ),
                      ),
                    if (_currentDisplayText.isNotEmpty)
                      Row(
                        children: [
                          Text(
                            '> $_currentDisplayText',
                            style: const TextStyle(
                              color: AppColors.hackerGreen,
                              fontSize: 12,
                              fontFamily: 'Courier',
                            ),
                          ),
                          _buildCursor(),
                        ],
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCursor() {
    return AnimatedBuilder(
      animation: _typewriterController,
      builder: (context, child) {
        return Opacity(
          opacity: (sin(_typewriterController.value * pi * 4) + 1) / 2,
          child: const Text(
            '█',
            style: TextStyle(
              color: AppColors.hackerGreen,
              fontSize: 12,
              fontFamily: 'Courier',
            ),
          ),
        );
      },
    );
  }

  Widget _buildSystemInfo() {
    final stats = AppUtils.generateSystemStats();

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.black54,
          border: Border.all(color: AppColors.neonCyan, width: 1),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem('CPU', '${stats['cpu']}%'),
            _buildStatItem('RAM', '${stats['ram']}%'),
            _buildStatItem('NET', '${stats['network']}%'),
            _buildStatItem('DISK', '${stats['disk']}%'),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: AppColors.neonCyan,
            fontSize: 10,
            fontFamily: 'Courier',
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(
            color: AppColors.primaryText,
            fontSize: 12,
            fontFamily: 'Courier',
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
