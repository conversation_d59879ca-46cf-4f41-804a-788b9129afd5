🧾 Project Title:

Hacker-Themed Cross-Platform Music Player (Flutter)

⸻

🎯 Objective:

To build a futuristic, hacker/cyberpunk-style music player app using Flutter that:
	•	Streams music from YouTube
	•	Allows offline download & playback
	•	Works seamlessly across Android and iOS
	•	Delivers a responsive, immersive hacker-themed UI

⸻

📱 Target Platforms:
	•	Android (all major screen sizes)
	•	iOS (iPhones, iPads)
	•	Optional: Web/Desktop support in future

⸻

🎨 Theme:

Hacker/Glitch/Cyberpunk UI
	•	Dark theme with neon highlights (green/cyan/purple/red)
	•	Matrix-style code rains, terminal popups, glitch animations
	•	Loading screens that mimic “bootloaders”
	•	Floating system resource HUD (RAM/CPU usage simulation)
	•	Custom icons/fonts to match hacker aesthetic

⸻

🧩 Core Features:

🔊 Music Playback
	•	Play music from YouTube links/search
	•	Pause/Play/Stop/Seek
	•	Shuffle & repeat modes
	•	Playlist & Queue management
	•	Mini-player with controls
	•	Background playback support

📥 Offline Mode
	•	Download songs from YouTube (as audio)
	•	Store downloaded songs locally
	•	Offline library & playback
	•	Auto-sync when online

🧠 YouTube Integration
	•	Search YouTube videos
	•	Extract audio from YouTube (use youtube_explode_dart)
	•	Show video thumbnails, titles, artists
	•	Handle region-restricted content gracefully

💾 Library Management
	•	Favorite songs
	•	Create/rename/delete playlists
	•	Song metadata (title, artist, duration)
	•	Sort/filter by artist, album, or recent plays

📱 Adaptive UI/UX
	•	Material Design (Android)
	•	Cupertino Widgets (iOS)
	•	Responsive layouts (phone/tablet)
	•	System theme detection (dark/light)

💡 Hacker Theme Add-ons
	•	Terminal-style splash screen / loader
	•	Animated glitch effects on hover/tap
	•	Simulated “RAM/CPU” indicator
	•	Threat-detection popups or random “access logs”
	•	Stylized “Download complete” with hacker log effect

🔒 Permissions
	•	Storage (for downloads)
	•	Internet (streaming)
	•	Background service (audio playback)

⸻

🧱 Architecture

🛠 Frameworks & Libraries
	•	Flutter
	•	youtube_explode_dart (for YouTube audio)
	•	just_audio or audioplayers (for playback)
	•	hive or sqflite (for local DB)
	•	provider, riverpod or bloc (for state management)
	•	path_provider, permission_handler
	•	flutter_downloader (for background downloads)

⸻

🧪 Testing
	•	Unit & widget tests for UI
	•	Playback stress testing
	•	Offline mode reliability
	•	Permissions across Android/iOS

⸻

🚀 Stretch Goals (Optional Later)
	•	Lyrics support
	•	Sound visualizer (in terminal style)
	•	Voice command control
	•	Bluetooth/Headset detection
	•	Theme customization (choose between hacker/cyberpunk/matrix modes)

⸻

🧠 Sample Prompt to Feed an AI (for code-gen or UI gen):

“Develop a Flutter app that works on both Android and iOS. The app should be a hacker-themed music player. It must include:
	•	YouTube search and music streaming
	•	Offline audio downloads using youtube_explode_dart
	•	just_audio integration for playback
	•	A dark, neon-glitch hacker UI with system resource simulation
	•	Terminal-style bootloader splash screen
	•	Playlist, favorites, and queue support
	•	Responsive UI that adapts to Android/iOS themes
	•	Background audio playback and offline mode
Use clean architecture, riverpod for state management, and store downloaded files in local storage with permission handling.”