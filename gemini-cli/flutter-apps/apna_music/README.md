# 🎵 Apna Music - Hacker Edition

A futuristic, hacker/cyberpunk-themed cross-platform music player built with Flutter.

## ✨ Features

- 🎯 **Hacker-themed UI** with Matrix-style animations and terminal aesthetics
- 🎵 **YouTube Integration** for streaming and downloading music
- 📱 **Cross-platform** support (Android, iOS, macOS, Windows, Linux)
- 💾 **Offline Playback** with local storage
- 🎨 **Cyberpunk Design** with neon colors and glitch effects
- 🔊 **Advanced Audio Controls** with background playback

## 🏗️ Architecture

This project follows **Clean Architecture** principles:

```
lib/
├── core/                 # Core utilities and configurations
│   ├── constants/        # App constants
│   ├── errors/          # Error handling
│   ├── network/         # Network utilities
│   ├── themes/          # Hacker theme configuration
│   └── utils/           # Utility functions
├── data/                # Data layer
│   ├── datasources/     # Remote and local data sources
│   ├── models/          # Data models
│   └── repositories/    # Repository implementations
├── domain/              # Business logic layer
│   ├── entities/        # Core entities
│   ├── repositories/    # Repository interfaces
│   └── usecases/        # Business use cases
└── presentation/        # UI layer
    ├── pages/           # Screen widgets
    ├── widgets/         # Reusable UI components
    └── providers/       # State management (Riverpod)
```

## 🚀 Getting Started

### Prerequisites

- Flutter SDK (>=3.8.1)
- Dart SDK
- Platform-specific requirements for your target platform

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   flutter pub get
   ```
3. Run the app:
   ```bash
   flutter run
   ```

## 📦 Dependencies

### Core Dependencies

- `flutter_riverpod` - State management
- `hive` & `hive_flutter` - Local database
- `youtube_explode_dart` - YouTube integration
- `just_audio` - Audio playback
- `http` - Network requests
- `path_provider` - File system access
- `permission_handler` - Permissions management

### Development Dependencies

- `build_runner` - Code generation
- `hive_generator` - Hive model generation
- `json_serializable` - JSON serialization
- `freezed` - Immutable classes

## 🎨 Theme

The app features a custom hacker/cyberpunk theme with:

- **Primary Colors**: Matrix Green (#00FF41), Neon Cyan (#00FFFF)
- **Accent Colors**: Cyberpunk Purple (#9D00FF), Danger Red (#FF0040)
- **Typography**: Courier font family for terminal aesthetics
- **Effects**: Glow effects, glitch animations, Matrix rain

## 🔧 Development Status

- ✅ Project setup and dependencies
- ✅ Clean architecture structure
- ✅ Hacker theme system
- ✅ Splash screen with terminal bootloader effect
- ✅ Basic navigation structure
- 🚧 YouTube integration (in progress)
- 🚧 Audio playback system (in progress)
- 🚧 Local storage and database (in progress)

## 📱 Screenshots

_Screenshots will be added as development progresses_

## 🤝 Contributing

This is a personal project, but suggestions and feedback are welcome!

## 📄 License

This project is for educational and personal use.
